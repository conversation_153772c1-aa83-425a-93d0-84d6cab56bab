# ECS EC2 Terraform Configuration

This Terraform configuration creates an ECS cluster running on EC2 instances for the mart-api application.

## Architecture

The configuration creates:

- **ECS Cluster**: EC2-based cluster for running containerized applications
- **Task Definition**: Defines the mart-api container with all required configurations
- **ECS Service**: Manages the desired number of running tasks
- **Auto Scaling Group**: Manages EC2 instances for the ECS cluster
- **Launch Template**: Defines the EC2 instance configuration
- **IAM Roles**: Required permissions for ECS instances and tasks
- **CloudWatch Log Group**: For application logging

## Files

- `main.tf`: Main Terraform configuration
- `variables.tf`: Variable definitions
- `outputs.tf`: Output definitions
- `providers.tf`: Provider configuration
- `user_data.sh`: EC2 instance initialization script
- `terraform.tfvars.example`: Example variable values

## Prerequisites

1. AWS CLI configured with appropriate credentials
2. Terraform installed (version >= 1.0)
3. Access to the specified VPC, subnets, and security groups
4. ECR repository with the container image
5. IAM roles and SSM parameters referenced in the task definition

## Usage

1. **Initialize Terraform**:
   ```bash
   terraform init
   ```

2. **Create terraform.tfvars**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your specific values
   ```

3. **Plan the deployment**:
   ```bash
   terraform plan
   ```

4. **Apply the configuration**:
   ```bash
   terraform apply
   ```

5. **Destroy resources** (when needed):
   ```bash
   terraform destroy
   ```

## Configuration Details

### Network Configuration
- VPC: `vpc-036c0231856bf15b6`
- Subnets: 6 subnets across multiple AZs
- Security Groups: 5 security groups for different access patterns

### Container Configuration
- **Image**: `270863951168.dkr.ecr.us-east-1.amazonaws.com/mart-api-ecr:29517f3`
- **CPU**: 4096 units
- **Memory**: 12000 MB
- **Port**: 8080 (container and host)
- **Network Mode**: bridge

### Environment Variables
- `SERVICE_TYPE`: TSAPI
- `ACTIVE_ENV`: stg

### Secrets (from SSM Parameter Store)
- `MARTGATEWAY_RDB_PASSWORD`
- `MARTGATEWAY_REDSHIFT_PASSWORD`
- `MARTGATEWAY_EOD_PASSWORD`
- `MARTGATEWAY_FIXED-INCOME_PASSWORD`

### EC2 Instance Configuration
- **Instance Type**: c5.xlarge
- **AMI**: Latest ECS-optimized Amazon Linux 2
- **Auto Scaling**: 1-3 instances
- **Placement Constraint**: c5 instance family

## Monitoring

- CloudWatch Container Insights enabled
- Application logs sent to `/ecs/tscache-mart-api-fargate-dev`
- EC2 instance metrics collected via CloudWatch agent

## Tags

All resources are tagged with:
- `PID`: PID0029
- `MANAGED`: terraform
- `SERVICEID`: ts00057
- `TID`: DATAAC

## Notes

- The configuration uses existing IAM roles (`mart-role-stg`)
- Container image tag should be updated as needed
- Auto Scaling Group will maintain at least 1 EC2 instance
- ECS service will ensure 1 task is always running
- The setup uses bridge networking mode for EC2 compatibility
