# Variables for ECS EC2 deployment

variable "vpc_id" {
  description = "VPC ID for the ECS cluster"
  type        = string
  default     = "vpc-036c0231856bf15b6"
}

variable "subnet_ids" {
  description = "List of subnet IDs for the ECS cluster"
  type        = list(string)
  default = [
    "subnet-08f0ece89d42f7d73",
    "subnet-0a3ba477686e70b4a",
    "subnet-0d0e5c6b9ac89b67f",
    "subnet-027635617843fbc07",
    "subnet-0a1c0ff43ccc5652c",
    "subnet-00fa9e860f858a4a2"
  ]
}

variable "security_group_ids" {
  description = "List of security group IDs for the ECS cluster"
  type        = list(string)
  default = [
    "sg-009b17a4e9b81a5f0",
    "sg-03afef83e7d6959c8",
    "sg-0bfc358885a343fed",
    "sg-0fd2a98817ac752f7",
    "sg-08a83e28c2216dc3c"
  ]
}

variable "cluster_name" {
  description = "Name of the ECS cluster"
  type        = string
  default     = "mart-api-ec2-cluster-stg"
}

variable "service_name" {
  description = "Name of the ECS service"
  type        = string
  default     = "mart-api-ec2-service-stg"
}

variable "task_family" {
  description = "Family name for the ECS task definition"
  type        = string
  default     = "mart-api-ec2-task-definition-stg"
}

variable "container_image" {
  description = "Docker image for the container"
  type        = string
  default     = "270863951168.dkr.ecr.us-east-1.amazonaws.com/mart-api-ecr:29517f3"
}

variable "container_cpu" {
  description = "CPU units for the container"
  type        = number
  default     = 4096
}

variable "container_memory" {
  description = "Memory for the container in MB"
  type        = number
  default     = 12000
}

variable "desired_count" {
  description = "Desired number of tasks to run"
  type        = number
  default     = 1
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "stg"
}

variable "tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default = {
    PID       = "PID0029"
    MANAGED   = "terraform"
    SERVICEID = "ts00057"
    TID       = "DATAAC"
  }
}
