在 ecs2文件夹下，创建terraform脚本
1. 创建ecs集群，使用C2来运行应用程序
2. 网络结果如下
vpc_id = "vpc-036c0231856bf15b6"

subnet_ids = [
  "subnet-08f0ece89d42f7d73",
  "subnet-0a3ba477686e70b4a",
  "subnet-0d0e5c6b9ac89b67f",
  "subnet-027635617843fbc07",
  "subnet-0a1c0ff43ccc5652c",
  "subnet-00fa9e860f858a4a2"
]

security_group_ids = [
  "sg-009b17a4e9b81a5f0",
  "sg-03afef83e7d6959c8",
  "sg-0bfc358885a343fed",
  "sg-0fd2a98817ac752f7",
  "sg-08a83e28c2216dc3c"
]


3.task definition sample
{
  "taskDefinitionArn": "arn:aws:ecs:us-east-1:270863951168:task-definition/mart-api-ec2-task-definition-stg:42",
  "containerDefinitions": [
    {
      "name": "mart-api-ec2-container-stg",
      "image": "270863951168.dkr.ecr.us-east-1.amazonaws.com/mart-api-ecr:29517f3",
      "cpu": 4096,
      "portMappings": [
        {
          "name": "mart-api-ec2-container-stg-8080-tcp",
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "SERVICE_TYPE",
          "value": "TSAPI"
        },
        {
          "name": "ACTIVE_ENV",
          "value": "stg"
        }
      ],
      "mountPoints": [],
      "volumesFrom": [],
      "secrets": [
        {
          "name": "MARTGATEWAY_RDB_PASSWORD",
          "valueFrom": "arn:aws:ssm:us-east-1:270863951168:parameter/MART/INVESTAPI_READER_UAT1"
        },
        {
          "name": "MARTGATEWAY_REDSHIFT_PASSWORD",
          "valueFrom": "arn:aws:ssm:us-east-1:270863951168:parameter/LAKEHOUSE/ADMIN_PASSWORD"
        },
        {
          "name": "MARTGATEWAY_EOD_PASSWORD",
          "valueFrom": "arn:aws:ssm:us-east-1:270863951168:parameter/MART/EOD/INVESTMENT_SERVICE_READER"
        },
        {
          "name": "MARTGATEWAY_FIXED-INCOME_PASSWORD",
          "valueFrom": "arn:aws:ssm:us-east-1:270863951168:parameter/MART/FIXEDINCOME_DB_PASSWORD"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/tscache-mart-api-fargate-dev",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "systemControls": []
    }
  ],
  "family": "mart-api-ec2-task-definition-stg",
  "taskRoleArn": "arn:aws:iam::270863951168:role/mart-role-stg",
  "executionRoleArn": "arn:aws:iam::270863951168:role/mart-role-stg",
  "networkMode": "bridge",
  "revision": 42,
  "volumes": [],
  "status": "ACTIVE",
  "requiresAttributes": [
    {
      "name": "com.amazonaws.ecs.capability.logging-driver.awslogs"
    },
    {
      "name": "ecs.capability.execution-role-awslogs"
    },
    {
      "name": "com.amazonaws.ecs.capability.ecr-auth"
    },
    {
      "name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"
    },
    {
      "name": "com.amazonaws.ecs.capability.task-iam-role"
    },
    {
      "name": "ecs.capability.execution-role-ecr-pull"
    },
    {
      "name": "ecs.capability.secrets.ssm.environment-variables"
    }
  ],
  "placementConstraints": [],
  "compatibilities": [
    "EC2"
  ],
  "requiresCompatibilities": [
    "EC2"
  ],
  "cpu": "4096",
  "memory": "12000",
  "registeredAt": "2025-07-16T22:50:36.139Z",
  "registeredBy": "arn:aws:sts::270863951168:assumed-role/AWSReservedSSO_mstar-operator_e7c98360f32db1a8/<EMAIL>",
  "tags": [
    {
      "key": "PID",
      "value": "PID0029"
    },
    {
      "key": "MANAGED",
      "value": "terraform"
    },
    {
      "key": "SERVICEID",
      "value": "ts00057"
    },
    {
      "key": "TID",
      "value": "DATAAC"
    }
  ]
}

