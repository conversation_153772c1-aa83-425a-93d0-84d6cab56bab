# Outputs for ECS EC2 deployment

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.mart_api_cluster.id
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.mart_api_cluster.arn
}

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.mart_api_cluster.name
}

output "task_definition_arn" {
  description = "ARN of the ECS task definition"
  value       = aws_ecs_task_definition.mart_api_task.arn
}

output "task_definition_family" {
  description = "Family of the ECS task definition"
  value       = aws_ecs_task_definition.mart_api_task.family
}

output "task_definition_revision" {
  description = "Revision of the ECS task definition"
  value       = aws_ecs_task_definition.mart_api_task.revision
}

output "service_id" {
  description = "ID of the ECS service"
  value       = aws_ecs_service.mart_api_service.id
}

output "service_name" {
  description = "Name of the ECS service"
  value       = aws_ecs_service.mart_api_service.name
}

output "service_cluster" {
  description = "Cluster of the ECS service"
  value       = aws_ecs_service.mart_api_service.cluster
}

output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.mart_api_logs.name
}

output "log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.mart_api_logs.arn
}
