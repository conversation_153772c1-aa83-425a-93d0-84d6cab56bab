# Example terraform.tfvars file
# Copy this file to terraform.tfvars and modify as needed

# Network Configuration
vpc_id = "vpc-036c0231856bf15b6"

subnet_ids = [
  "subnet-08f0ece89d42f7d73",
  "subnet-0a3ba477686e70b4a",
  "subnet-0d0e5c6b9ac89b67f",
  "subnet-027635617843fbc07",
  "subnet-0a1c0ff43ccc5652c",
  "subnet-00fa9e860f858a4a2"
]

security_group_ids = [
  "sg-009b17a4e9b81a5f0",
  "sg-03afef83e7d6959c8",
  "sg-0bfc358885a343fed",
  "sg-0fd2a98817ac752f7",
  "sg-08a83e28c2216dc3c"
]

# ECS Configuration
cluster_name    = "mart-api-ec2-cluster-stg"
service_name    = "mart-api-ec2-service-stg"
task_family     = "mart-api-ec2-task-definition-stg"
container_image = "270863951168.dkr.ecr.us-east-1.amazonaws.com/mart-api-ecr:29517f3"
container_cpu   = 4096
container_memory = 12000
desired_count   = 1
environment     = "stg"

# Tags
tags = {
  PID       = "PID0029"
  MANAGED   = "terraform"
  SERVICEID = "ts00057"
  TID       = "DATAAC"
}
