# ECS Cluster for EC2 deployment
resource "aws_ecs_cluster" "mart_api_cluster" {
  name = "mart-api-ec2-cluster-stg"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    PID       = "PID0029"
    MANAGED   = "terraform"
    SERVICEID = "ts00057"
    TID       = "DATAAC"
  }
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "mart_api_logs" {
  name              = "/ecs/tscache-mart-api-fargate-dev"
  retention_in_days = 7

  tags = {
    PID       = "PID0029"
    MANAGED   = "terraform"
    SERVICEID = "ts00057"
    TID       = "DATAAC"
  }
}

# ECS Task Definition
resource "aws_ecs_task_definition" "mart_api_task" {
  family                   = "mart-api-ec2-task-definition-stg"
  network_mode             = "bridge"
  requires_compatibilities = ["EC2"]
  cpu                      = "4096"
  memory                   = "12000"
  task_role_arn           = "arn:aws:iam::270863951168:role/mart-role-stg"
  execution_role_arn      = "arn:aws:iam::270863951168:role/mart-role-stg"

  container_definitions = jsonencode([
    {
      name      = "mart-api-ec2-container-stg"
      image     = "270863951168.dkr.ecr.us-east-1.amazonaws.com/mart-api-ecr:29517f3"
      cpu       = 4096
      essential = true

      portMappings = [
        {
          name          = "mart-api-ec2-container-stg-8080-tcp"
          containerPort = 8080
          hostPort      = 8080
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "SERVICE_TYPE"
          value = "TSAPI"
        },
        {
          name  = "ACTIVE_ENV"
          value = "stg"
        }
      ]

      secrets = [
        {
          name      = "MARTGATEWAY_RDB_PASSWORD"
          valueFrom = "arn:aws:ssm:us-east-1:270863951168:parameter/MART/INVESTAPI_READER_UAT1"
        },
        {
          name      = "MARTGATEWAY_REDSHIFT_PASSWORD"
          valueFrom = "arn:aws:ssm:us-east-1:270863951168:parameter/LAKEHOUSE/ADMIN_PASSWORD"
        },
        {
          name      = "MARTGATEWAY_EOD_PASSWORD"
          valueFrom = "arn:aws:ssm:us-east-1:270863951168:parameter/MART/EOD/INVESTMENT_SERVICE_READER"
        },
        {
          name      = "MARTGATEWAY_FIXED-INCOME_PASSWORD"
          valueFrom = "arn:aws:ssm:us-east-1:270863951168:parameter/MART/FIXEDINCOME_DB_PASSWORD"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/tscache-mart-api-fargate-dev"
          "awslogs-region"        = "us-east-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }

      mountPoints  = []
      volumesFrom  = []
      systemControls = []
    }
  ])

  tags = {
    PID       = "PID0029"
    MANAGED   = "terraform"
    SERVICEID = "ts00057"
    TID       = "DATAAC"
  }
}

# Data source for ECS optimized AMI
data "aws_ami" "ecs_optimized" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-ecs-hvm-*-x86_64-ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# IAM role for ECS instances
resource "aws_iam_role" "ecs_instance_role" {
  name = "mart-api-ecs-instance-role-stg"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Attach the ECS instance policy
resource "aws_iam_role_policy_attachment" "ecs_instance_role_policy" {
  role       = aws_iam_role.ecs_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

# IAM instance profile
resource "aws_iam_instance_profile" "ecs_instance_profile" {
  name = "mart-api-ecs-instance-profile-stg"
  role = aws_iam_role.ecs_instance_role.name

  tags = var.tags
}

# Launch template for ECS instances
resource "aws_launch_template" "ecs_launch_template" {
  name_prefix   = "mart-api-ecs-template-stg-"
  image_id      = data.aws_ami.ecs_optimized.id
  instance_type = "c5.xlarge"

  vpc_security_group_ids = var.security_group_ids

  iam_instance_profile {
    name = aws_iam_instance_profile.ecs_instance_profile.name
  }

  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    cluster_name = aws_ecs_cluster.mart_api_cluster.name
  }))

  tag_specifications {
    resource_type = "instance"
    tags = merge(var.tags, {
      Name = "mart-api-ecs-instance-stg"
    })
  }

  tags = var.tags
}

# Auto Scaling Group
resource "aws_autoscaling_group" "ecs_asg" {
  name                = "mart-api-ecs-asg-stg"
  vpc_zone_identifier = var.subnet_ids
  target_group_arns   = []
  health_check_type   = "EC2"
  health_check_grace_period = 300

  min_size         = 1
  max_size         = 3
  desired_capacity = 1

  launch_template {
    id      = aws_launch_template.ecs_launch_template.id
    version = "$Latest"
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = true
    propagate_at_launch = false
  }

  dynamic "tag" {
    for_each = var.tags
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }
}

# ECS Service
resource "aws_ecs_service" "mart_api_service" {
  name            = "mart-api-ec2-service-stg"
  cluster         = aws_ecs_cluster.mart_api_cluster.id
  task_definition = aws_ecs_task_definition.mart_api_task.arn
  desired_count   = 1
  launch_type     = "EC2"

  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }

  placement_constraints {
    type       = "memberOf"
    expression = "attribute:ecs.instance-type =~ c5.*"
  }

  depends_on = [aws_autoscaling_group.ecs_asg]

  tags = var.tags
}
